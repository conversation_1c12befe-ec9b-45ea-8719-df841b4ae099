---
# 🚀 Ansible Playbook for Adding Permanent Routes
# 作者: jqa
# 功能: 在目标主机上添加永久路由配置

- name: 📡 添加永久路由配置
  hosts: *************
  become: yes
  gather_facts: yes

  vars:
    # 🔧 路由配置变量
    gateway_ip: "*************"
    network_interface: "bond1"
    backup_dir: "/home/<USER>/route_backup_{{ ansible_date_time.epoch }}"

    # 📋 路由列表
    routes:
      - { network: "**********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "**********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "**********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "**********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "**********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "***********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "***********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "***********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "***********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "***********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "***********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "***********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "***********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "***********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "***********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "***********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "**********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "**********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "**********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "***********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "***********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "***********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "***********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "***********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "***********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "***********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "**********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "*********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "***********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "***********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "***********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "***********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "**********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "**********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "**********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "**********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "***********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "***********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "***********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "*********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "*********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "**********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "**********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "*********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "*********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "*********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "**********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "*********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "**********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "**********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "**********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "**********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "*********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "**********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "**********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "**********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "**********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "*********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "**********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "**********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "**********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "**********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "**********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "**********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "**********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "**********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "**********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "*********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "**********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "**********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "**********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "**********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "**********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "*********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "**********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "*********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "**********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "**********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "**********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "*********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "**********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "**********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "**********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "**********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "**********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "**********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "**********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "**********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "**********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "*********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "**********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "**********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "*********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "**********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "*********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "*********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "*********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "*********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "*********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "*********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "*********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "*********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "*********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "*********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "*********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "*********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "*********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "*********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "*********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "*********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "*********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "*********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "*********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "*********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "*********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "*********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "*********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "*********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "*********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "*********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "*********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }
      - { network: "*********/16", gateway: "{{ gateway_ip }}", device: "{{ network_interface }}" }

  tasks:
    # 🔍 系统信息收集
    - name: 📊 显示系统信息
      debug:
        msg: |
          🖥️  主机名: {{ ansible_hostname }}
          🐧 操作系统: {{ ansible_distribution }} {{ ansible_distribution_version }}
          🌐 网络接口: {{ ansible_interfaces }}
          📍 IP地址: {{ ansible_default_ipv4.address }}

    # 🔧 检查网络接口
    - name: 🔍 检查网络接口是否存在
      shell: ip link show {{ network_interface }}
      register: interface_check
      failed_when: false
      changed_when: false

    - name: ❌ 网络接口不存在时退出
      fail:
        msg: "❌ 网络接口 {{ network_interface }} 不存在！请检查配置。"
      when: interface_check.rc != 0

    # 📁 创建备份目录
    - name: 📁 创建备份目录
      file:
        path: "{{ backup_dir }}"
        state: directory
        mode: '0755'

    # 💾 备份现有路由表
    - name: 💾 备份当前路由表
      shell: ip route show > {{ backup_dir }}/routes_backup.txt
      changed_when: false

    # 💾  检查是否存在永久路由文件
    - name: 检查是否存在永久路由文件
      stat:
        path: "/etc/sysconfig/network-scripts/route-{{ network_interface }}"
      register: file_check

    - name: 备份永久路由文件
      copy:
        src: "/etc/sysconfig/network-scripts/route-{{ network_interface }}"
        dest: "{{ backup_dir }}/route-{{ network_interface }}.bak"
        remote_src: yes
      when: file_check.stat.exists

    # 📝 为 CentOS/RHEL 系统添加永久路由
    - name: 📝 为 CentOS/RHEL 添加永久路由配置
      lineinfile:
        path: "/etc/sysconfig/network-scripts/route-{{ network_interface }}"
        line: "{{ item.network }} via {{ item.gateway }} dev {{ item.device }}"
        create: yes
      loop: "{{ routes }}"

    # 🚀 立即添加路由（临时）
    - name: 🚀 立即添加路由到路由表
      command: ip route add {{ item.network }} via {{ item.gateway }} dev {{ item.device }}
      loop: "{{ routes }}"
      register: route_add_result
      failed_when: false
      changed_when: route_add_result.rc == 0

    # ✅ 验证路由添加结果
    - name: ✅ 验证路由是否添加成功
      shell: ip route show | grep "{{ item.network }}"
      loop: "{{ routes }}"
      register: route_verify
      failed_when: false
      changed_when: false

    # 📊 显示添加结果统计
    - name: 📊 显示路由添加统计
      debug:
        msg: |
          🎯 路由添加完成！
          ✅ 成功添加: {{ route_add_result.results | selectattr('rc', 'equalto', 0) | list | length }} 条路由
          ⚠️  已存在: {{ route_add_result.results | selectattr('rc', 'equalto', 2) | list | length }} 条路由
          ❌ 添加失败: {{ route_add_result.results | rejectattr('rc', 'in', [0, 2]) | list | length }} 条路由

    # 💾 保存最终路由表
    - name: 💾 保存最终路由表
      shell: ip route show > {{ backup_dir }}/routes_final.txt
      changed_when: false

    # 📋 显示备份信息
    - name: 📋 显示备份信息
      debug:
        msg: |
          📁 备份目录: {{ backup_dir }}
          📄 备份文件:
            - routes_backup.txt (原始路由表)
            - routes_final.txt (最终路由表)