---
# 🔄 Ansible 路由回滚脚本
# 功能: 回滚之前添加的路由配置

- name: 🔄 回滚路由配置
  hosts: all
  become: yes
  gather_facts: yes
  
  vars:
    # 🔧 配置变量
    gateway_ip: "*************"
    network_interface: "bond1"
    
    # 📋 需要删除的路由列表（与添加时相同）
    routes_to_remove:
      - "**********/16"
      - "**********/16"
      - "**********/16"
      - "**********/16"
      - "**********/16"
      - "***********/16"
      - "***********/16"
      - "***********/16"
      - "***********/16"
      - "***********/16"
      - "***********/16"
      - "***********/16"
      - "***********/16"
      - "***********/16"
      - "***********/16"
      - "***********/16"
      - "**********/16"
      - "**********/16"
      - "**********/16"
      - "***********/16"
      - "***********/16"
      - "***********/16"
      - "***********/16"
      - "***********/16"
      - "***********/16"
      - "***********/16"
      - "**********/16"
      - "*********/16"
      - "***********/16"
      - "***********/16"
      - "***********/16"
      - "***********/16"
      - "**********/16"
      - "**********/16"
      - "**********/16"
      - "136.15.0.0/16"
      - "136.192.0.0/16"
      - "136.221.0.0/16"
      - "136.255.0.0/16"
      - "136.4.0.0/16"
      - "136.6.0.0/16"
      - "136.64.0.0/16"
      - "136.96.0.0/16"
      - "137.0.0.0/16"
      - "137.1.0.0/16"
      - "137.4.0.0/16"
      - "137.64.0.0/16"
      - "137.8.0.0/16"
      - "17.0.0.0/16"
      - "17.100.0.0/16"
      - "17.101.0.0/16"
      - "17.108.0.0/16"
      - "17.109.0.0/16"
      - "17.11.0.0/16"
      - "17.110.0.0/16"
      - "17.111.0.0/16"
      - "17.116.0.0/16"
      - "17.117.0.0/16"
      - "17.12.0.0/16"
      - "17.120.0.0/16"
      - "17.121.0.0/16"
      - "17.122.0.0/16"
      - "17.123.0.0/16"
      - "17.124.0.0/16"
      - "17.125.0.0/16"
      - "17.126.0.0/16"
      - "17.128.0.0/16"
      - "17.129.0.0/16"
      - "17.13.0.0/16"
      - "17.132.0.0/16"
      - "17.133.0.0/16"
      - "17.134.0.0/16"
      - "17.140.0.0/16"
      - "17.148.0.0/16"
      - "17.15.0.0/16"
      - "17.156.0.0/16"
      - "17.16.0.0/16"
      - "17.160.0.0/16"
      - "17.168.0.0/16"
      - "17.169.0.0/16"
      - "17.17.0.0/16"
      - "17.170.0.0/16"
      - "17.171.0.0/16"
      - "17.172.0.0/16"
      - "17.173.0.0/16"
      - "17.176.0.0/16"
      - "17.177.0.0/16"
      - "17.180.0.0/16"
      - "17.186.0.0/16"
      - "17.188.0.0/16"
      - "17.20.0.0/16"
      - "17.213.0.0/16"
      - "17.235.0.0/16"
      - "17.24.0.0/16"
      - "17.255.0.0/16"
      - "17.28.0.0/16"
      - "17.29.0.0/16"
      - "17.3.0.0/16"
      - "17.30.0.0/16"
      - "17.31.0.0/16"
      - "17.32.0.0/16"
      - "17.36.0.0/16"
      - "17.37.0.0/16"
      - "17.38.0.0/16"
      - "17.39.0.0/16"
      - "17.4.0.0/16"
      - "17.40.0.0/16"
      - "17.44.0.0/16"
      - "17.45.0.0/16"
      - "17.5.0.0/16"
      - "17.52.0.0/16"
      - "17.56.0.0/16"
      - "17.57.0.0/16"
      - "17.58.0.0/16"
      - "*********/16"
      - "*********/16"
      - "*********/16"
      - "*********/16"
      - "*********/16"
      - "*********/16"
      - "********/16"
      - "*********/16"
      - "*********/16"
      - "********/16"
      - "*********/16"
      - "*********/16"
      - "*********/16"
      - "*********/16"

  tasks:
    # 📊 显示回滚信息
    - name: 📊 显示回滚操作信息
      debug:
        msg: |
          🔄 开始回滚路由配置
          🖥️  主机: {{ ansible_hostname }}
          🌐 接口: {{ network_interface }}
          📋 将删除 {{ routes_to_remove | length }} 条路由

    # ⚠️ 确认操作
    - name: ⚠️ 回滚确认提示
      pause:
        prompt: |
          
          ⚠️  警告: 即将删除所有已配置的路由！
          
          📋 操作详情:
          - 主机: {{ ansible_hostname }}
          - 接口: {{ network_interface }}
          - 路由数量: {{ routes_to_remove | length }}
          
          🤔 确定要继续吗？(yes/no)
      register: rollback_confirmation
      when: ansible_check_mode is not defined

    # ❌ 取消操作
    - name: ❌ 用户取消回滚操作
      fail:
        msg: "❌ 用户取消了回滚操作"
      when: 
        - ansible_check_mode is not defined
        - rollback_confirmation.user_input | lower != 'yes'

    # 💾 备份当前路由表
    - name: 💾 备份当前路由表
      shell: ip route show > /tmp/routes_before_rollback_{{ ansible_date_time.epoch }}.txt
      changed_when: false

    # 🗑️ 删除临时路由
    - name: 🗑️ 从路由表中删除路由
      command: ip route del {{ item }} via {{ gateway_ip }} dev {{ network_interface }}
      loop: "{{ routes_to_remove }}"
      register: route_del_result
      failed_when: false
      changed_when: route_del_result.rc == 0

    # 🔍 检查操作系统类型
    - name: 🔍 检测操作系统类型
      set_fact:
        is_redhat: "{{ ansible_os_family == 'RedHat' }}"
        is_debian: "{{ ansible_os_family == 'Debian' }}"

    # 🗑️ 删除 CentOS/RHEL 永久路由配置
    - name: 🗑️ 删除 CentOS/RHEL 路由配置文件
      file:
        path: "/etc/sysconfig/network-scripts/route-{{ network_interface }}"
        state: absent
      when: is_redhat
      notify: restart_network_redhat

    # 🗑️ 删除 Ubuntu/Debian 永久路由配置
    - name: 🗑️ 删除 Ubuntu/Debian 路由配置文件
      file:
        path: /etc/netplan/99-custom-routes.yaml
        state: absent
      when: is_debian
      notify: apply_netplan

    # 📊 显示删除结果统计
    - name: 📊 显示路由删除统计
      debug:
        msg: |
          🔄 路由回滚完成！
          ✅ 成功删除: {{ route_del_result.results | selectattr('rc', 'equalto', 0) | list | length }} 条路由
          ⚠️  不存在: {{ route_del_result.results | selectattr('rc', 'equalto', 2) | list | length }} 条路由
          ❌ 删除失败: {{ route_del_result.results | rejectattr('rc', 'in', [0, 2]) | list | length }} 条路由

    # ✅ 验证回滚结果
    - name: ✅ 验证路由是否已删除
      shell: ip route show | grep "{{ item }}" || echo "DELETED"
      loop: "{{ routes_to_remove }}"
      register: route_verify_deleted
      changed_when: false

    # 💾 保存回滚后路由表
    - name: 💾 保存回滚后路由表
      shell: ip route show > /tmp/routes_after_rollback_{{ ansible_date_time.epoch }}.txt
      changed_when: false

    # 🎉 回滚完成提示
    - name: 🎉 回滚操作完成
      debug:
        msg: |
          ✅ 路由回滚操作已完成！
          
          📁 备份文件:
          - /tmp/routes_before_rollback_{{ ansible_date_time.epoch }}.txt
          - /tmp/routes_after_rollback_{{ ansible_date_time.epoch }}.txt
          
          💡 提示: 如需重新添加路由，请运行主配置脚本

  # 🔄 处理器定义
  handlers:
    - name: restart_network_redhat
      service:
        name: network
        state: restarted
      when: is_redhat

    - name: apply_netplan
      command: netplan apply
      when: is_debian
