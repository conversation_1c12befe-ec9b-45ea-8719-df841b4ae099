# 🚀 Ansible 永久路由配置脚本

这是一个用于在Linux服务器上批量添加永久路由的Ansible自动化脚本。

## 📋 功能特性

- ✅ **多系统支持**: 支持 CentOS/RHEL 和 Ubuntu/Debian 系统
- ✅ **永久配置**: 添加的路由在系统重启后仍然有效
- ✅ **批量操作**: 一次性添加128条路由规则
- ✅ **安全备份**: 自动备份现有路由配置
- ✅ **验证机制**: 自动验证路由添加结果
- ✅ **回滚支持**: 提供完整的回滚脚本
- ✅ **详细日志**: 提供详细的执行日志和统计信息

## 📁 文件结构

```
添加路由/
├── ansible-playbook.yml     # 主要的路由配置脚本
├── rollback-playbook.yml    # 路由回滚脚本
├── inventory.ini            # 主机清单文件
├── group_vars/
│   └── all.yml             # 全局变量配置
└── README.md               # 使用说明文档
```

## 🔧 配置说明

### 路由信息
- **网关IP**: *************
- **网络接口**: bond1
- **路由数量**: 128条
- **网络范围**: 10.x.x.x, 17.x.x.x, 132.x.x.x, 133.x.x.x, 134.x.x.x, 135.x.x.x, 136.x.x.x, 137.x.x.x

### 系统支持
- **CentOS/RHEL**: 使用 `/etc/sysconfig/network-scripts/route-<interface>` 文件
- **Ubuntu/Debian**: 使用 Netplan 配置文件

## 🚀 使用方法

### 1. 准备工作

#### 安装 Ansible
```bash
# CentOS/RHEL
sudo yum install ansible -y

# Ubuntu/Debian
sudo apt update && sudo apt install ansible -y
```

#### 配置SSH密钥认证
```bash
# 生成SSH密钥对
ssh-keygen -t rsa -b 4096

# 复制公钥到目标主机
ssh-copy-id user@target-host
```

### 2. 配置主机清单

编辑 `inventory.ini` 文件，添加您的目标主机：

```ini
[route_servers]
server1 ansible_host=************ ansible_user=root
server2 ansible_host=************ ansible_user=admin ansible_become=yes
server3 ansible_host=************ ansible_user=ubuntu

[route_servers:vars]
ansible_ssh_private_key_file=~/.ssh/id_rsa
ansible_become=yes
```

### 3. 执行路由配置

#### 🔍 检查模式（推荐先执行）
```bash
ansible-playbook -i inventory.ini ansible-playbook.yml --check
```

#### 🚀 正式执行
```bash
ansible-playbook -i inventory.ini ansible-playbook.yml
```

#### 📊 详细输出模式
```bash
ansible-playbook -i inventory.ini ansible-playbook.yml -v
```

#### 🎯 指定特定主机
```bash
ansible-playbook -i inventory.ini ansible-playbook.yml --limit server1
```

### 4. 验证配置结果

#### 检查路由表
```bash
# 在目标主机上执行
ip route show | grep "*************"
```

#### 测试连通性
```bash
# 测试特定网络的连通性
ping -c 3 **********
ping -c 3 **********
```

### 5. 回滚操作

如果需要删除添加的路由：

```bash
ansible-playbook -i inventory.ini rollback-playbook.yml
```

## ⚙️ 高级配置

### 自定义变量

您可以在 `group_vars/all.yml` 中修改以下配置：

```yaml
# 网络配置
network_config:
  gateway_ip: "*************"
  interface: "bond1"

# 备份配置
backup_config:
  keep_backup: true
  backup_retention_days: 7

# 验证配置
verification_config:
  test_connectivity: false
  ping_timeout: 5
```

### 环境特定配置

```bash
# 生产环境
ansible-playbook -i inventory.ini ansible-playbook.yml -e "environment=production"

# 测试环境
ansible-playbook -i inventory.ini ansible-playbook.yml -e "environment=staging"
```

## 🔍 故障排除

### 常见问题

#### 1. 网络接口不存在
```
❌ 网络接口 bond1 不存在！请检查配置。
```
**解决方案**: 检查目标主机的网络接口名称，修改 `network_interface` 变量

#### 2. 权限不足
```
Permission denied
```
**解决方案**: 确保用户有sudo权限，或使用root用户执行

#### 3. 路由已存在
```
RTNETLINK answers: File exists
```
**说明**: 这是正常现象，表示路由已经存在，脚本会自动处理

### 调试命令

#### 检查Ansible连接
```bash
ansible -i inventory.ini all -m ping
```

#### 检查目标主机信息
```bash
ansible -i inventory.ini all -m setup
```

#### 手动检查网络接口
```bash
ansible -i inventory.ini all -m shell -a "ip link show"
```

## 📊 执行结果示例

```
🎯 路由添加完成！
✅ 成功添加: 120 条路由
⚠️  已存在: 8 条路由
❌ 添加失败: 0 条路由

📁 备份目录: /tmp/route_backup_1234567890
📄 备份文件:
  - routes_backup.txt (原始路由表)
  - routes_final.txt (最终路由表)
```

## 🔐 安全注意事项

1. **备份重要**: 脚本会自动备份现有配置，但建议手动备份重要系统
2. **测试环境**: 建议先在测试环境验证脚本功能
3. **权限控制**: 使用最小权限原则，避免使用root用户
4. **网络影响**: 大量路由可能影响网络性能，请评估后执行

## 📞 技术支持

如果遇到问题，请检查：
1. Ansible版本兼容性
2. 目标主机的网络配置
3. SSH连接和权限设置
4. 防火墙和安全组配置

## 📝 更新日志

- **v1.0**: 初始版本，支持基本路由添加功能
- **v1.1**: 添加回滚功能和详细日志
- **v1.2**: 增强错误处理和系统兼容性

---

🎉 **祝您使用愉快！** 如有问题，请参考故障排除部分或联系技术支持。
