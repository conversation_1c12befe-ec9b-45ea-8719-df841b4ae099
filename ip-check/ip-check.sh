#!/bin/bash

# ========================================================
# 端口连通性批量测试脚本 (ip-check.sh)
# 作者：jqa
# 版本：1.3
# 描述：批量测试多个主机的TCP/UDP端口连通性和Ping连通性
#       支持Excel和HTML独立导出，HTML无依赖，浏览器友好
# 更新：2025-01-21
# ========================================================

# 定义颜色变量
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[0;33m'
NC='\033[0m' # 无颜色

# 默认参数
TIMEOUT=3
MODE="tcp"
VERBOSE=0
OUTPUT_FILE=""
EXCEL_FILE=""
HTML_FILE=""
PARALLEL=0
MAX_PARALLEL=10
INPUT_FILE=""
PING_COUNT=3 # 新增：ping的次数

# 初始化计数器
TOTAL=0
SUCCESS=0
FAILED=0

# 临时文件（用于并行处理）
TMP_DIR=$(mktemp -d)
TASKS_FILE="${TMP_DIR}/tasks.txt"
RESULTS_FILE="${TMP_DIR}/results.txt"

# 函数：显示帮助信息
show_help() {
    echo -e "${BLUE}端口连通性批量测试脚本${NC}"
    echo ""
    echo "用法: $0 [选项] [主机:端口 ...]"
    echo ""
    echo "选项:"
    echo "  -h, --help            显示此帮助信息"
    echo "  -f, --file FILE       从指定文件读取主机和端口列表（支持 主机:端口 或 主机 端口）"
    echo "  -t, --timeout SEC     设置连接超时时间（默认：${TIMEOUT}秒）"
    echo "  -p, --protocol PROTO  设置协议类型，tcp、udp或ping（默认：${MODE}）"
    echo "  -c, --count NUM       设置ping测试的次数（默认：${PING_COUNT}次，仅ping模式有效）"
    echo "  -v, --verbose         输出详细信息"
    echo "  -o, --output FILE     将结果输出到CSV文件"
    echo "  -e, --excel FILE      导出Excel文件（需要Python3+openpyxl）📊"
    echo "  -H, --html FILE       导出HTML表格文件（无依赖，浏览器友好）🌐"
    echo "  -P, --parallel        启用并行测试"
    echo "  -j, --jobs NUM        最大并行任务数（默认：${MAX_PARALLEL}）"
    echo ""
    echo "示例:"
    echo "  $0 google.com:80 github.com:443"
    echo "  $0 *********** 22 localhost 80  # 空格分隔格式"
    echo "  $0 --file hosts.txt --timeout 5 --protocol tcp"
    echo "  $0 --protocol ping google.com github.com"
    echo "  $0 --parallel --jobs 20 --file large_hosts_list.txt"
    echo "  $0 --file hosts.txt --excel results.xlsx  # 导出Excel文件"
    echo "  $0 --file hosts.txt --html results.html   # 导出HTML表格文件"
    echo ""
    echo "文件格式示例 (hosts.txt):"
    echo "  # 冒号分隔格式"
    echo "  ***********:22"
    echo "  example.com:80"
    echo "  myserver.local:443"
    echo "  # 空格分隔格式"
    echo "  *********** 22"
    echo "  example.com 80"
    echo "  myserver.local 443"
    echo "  # 对于ping测试，可以只指定主机名或IP，无需端口号"
    echo "  google.com"
    echo ""
}

# 函数：检查依赖工具
check_dependencies() {
    local missing=0

    # 检查 nc 是否存在
    if ! command -v nc &>/dev/null; then
        echo -e "${RED}错误: 未找到 nc 命令。请安装 netcat 工具。${NC}"
        echo -e "Debian/Ubuntu: sudo apt-get install netcat"
        echo -e "CentOS/RHEL: sudo yum install nc"
        echo -e "macOS: brew install netcat"
        missing=1
    fi

    # 如果需要导出Excel，检查Excel支持情况
    if [ -n "$EXCEL_FILE" ]; then
        echo -e "${BLUE}🔍 检测Excel导出支持情况...${NC}"

        # 检测完整Excel支持
        if command -v python3 &>/dev/null && python3 -c "import openpyxl" &>/dev/null 2>&1; then
            echo -e "${GREEN}✅ 完整Excel支持: Python3 + openpyxl${NC}"
        # 检测增强CSV支持
        elif command -v python3 &>/dev/null; then
            echo -e "${YELLOW}⚠️  部分支持: Python3可用，将使用增强CSV格式${NC}"
            echo -e "${BLUE}💡 如需完整Excel功能，可安装openpyxl: pip install openpyxl${NC}"
        else
            echo -e "${RED}❌ Excel导出不可用: 缺少Python3环境${NC}"
            echo -e "${BLUE}💡 建议安装Python3以获得Excel导出功能${NC}"
            echo -e "${YELLOW}💡 或者使用 -H/--html 选项导出HTML表格文件${NC}"
            exit 1
        fi

        echo -e "${GREEN}✅ Excel导出功能已就绪${NC}"
    fi

    # 如果需要导出HTML，无需特殊依赖
    if [ -n "$HTML_FILE" ]; then
        echo -e "${BLUE}🔍 HTML导出无需额外依赖，已就绪${NC}"
    fi

    # 对于并行模式，检查是否有 GNU parallel
    if [ $PARALLEL -eq 1 ]; then
        if ! command -v parallel &>/dev/null; then
            echo -e "${YELLOW}警告: 未找到 parallel 命令。将使用后台任务进行并行处理。${NC}"
            echo -e "如需更高效的并行处理，请安装 GNU parallel:"
            echo -e "Debian/Ubuntu: sudo apt-get install parallel"
            echo -e "CentOS/RHEL: sudo yum install parallel"
            echo -e "macOS: brew install parallel"
        fi
    fi

    if [ $missing -eq 1 ]; then
        exit 1
    fi
}

# 函数：解析主机和端口（支持冒号和空格分隔）
parse_host_port() {
    local input=$1
    local host=""
    local port=""

    # 如果是ping模式，只需要主机名
    if [ "$MODE" = "ping" ]; then
        # 移除可能的端口部分，只保留主机名
        if [[ $input =~ ^([^:[:space:]]+)[:[:space:]]+.*$ ]]; then
            host="${BASH_REMATCH[1]}"
        else
            host="$input"
        fi
        echo "$host"
        return 0
    fi

    # TCP/UDP模式需要解析主机和端口
    # 支持格式: host:port 或 host port 或 host  port (多个空格)
    if [[ $input =~ ^([^:[:space:]]+):([0-9]+)$ ]]; then
        # 冒号分隔格式: host:port
        host="${BASH_REMATCH[1]}"
        port="${BASH_REMATCH[2]}"
    elif [[ $input =~ ^([^[:space:]]+)[[:space:]]+([0-9]+)$ ]]; then
        # 空格分隔格式: host port 或 host  port
        host="${BASH_REMATCH[1]}"
        port="${BASH_REMATCH[2]}"
    else
        echo ""
        return 1
    fi

    echo "$host:$port"
    return 0
}

# 函数：验证IP地址或主机名和端口
validate_host_port() {
    local input=$1
    local parsed_result
    local host
    local port

    # 解析主机和端口
    parsed_result=$(parse_host_port "$input")
    if [ $? -ne 0 ] || [ -z "$parsed_result" ]; then
        if [ "$MODE" = "ping" ]; then
            echo -e "${RED}错误: 无效的主机格式 '$input'${NC}"
        else
            echo -e "${RED}错误: 无效的主机:端口格式 '$input'. 请使用 '主机:端口' 或 '主机 端口' 格式${NC}"
        fi
        return 1
    fi

    # 如果是ping模式，直接返回成功
    if [ "$MODE" = "ping" ]; then
        return 0
    fi

    # TCP/UDP模式需要验证端口
    host=${parsed_result%:*}
    port=${parsed_result#*:}

    # 验证端口范围
    if [ $port -lt 1 ] || [ $port -gt 65535 ]; then
        echo -e "${RED}错误: 无效的端口号 '$port'. 端口必须在 1-65535 范围内${NC}"
        return 1
    fi

    return 0
}

# 函数：测试单个主机端口
test_connection() {
    local input=$1
    local parsed_result
    local host
    local port
    local start_time=$(date +%s.%N)
    local status
    local duration

    # 解析输入格式
    parsed_result=$(parse_host_port "$input")
    if [ $? -ne 0 ] || [ -z "$parsed_result" ]; then
        echo "$input,错误,格式无效,0" >>"$RESULTS_FILE"
        return 1
    fi

    # 根据模式提取主机和端口
    if [ "$MODE" = "ping" ]; then
        host="$parsed_result"
        port="PING"
    else
        host=${parsed_result%:*}
        port=${parsed_result#*:}
    fi

    # 检查主机和端口格式
    if ! validate_host_port "$input"; then
        echo "$input,错误,格式无效,0" >>"$RESULTS_FILE"
        return 1
    fi

    # 根据协议进行不同测试
    if [ "$MODE" = "tcp" ]; then
        if [ $VERBOSE -eq 1 ]; then
            echo -e "${BLUE}测试 TCP 连接到 $host:$port (超时: ${TIMEOUT}s)${NC}"
        fi

        # 测试 TCP 连接
        if nc -z -w "$TIMEOUT" "$host" "$port" 2>/dev/null; then
            status="成功"
        else
            status="失败"
        fi
    elif [ "$MODE" = "udp" ]; then
        if [ $VERBOSE -eq 1 ]; then
            echo -e "${BLUE}测试 UDP 连接到 $host:$port (超时: ${TIMEOUT}s)${NC}"
        fi

        # 测试 UDP 连接 (注意：UDP检测不够可靠)
        if nc -z -u -w "$TIMEOUT" "$host" "$port" 2>/dev/null; then
            status="成功"
        else
            status="失败"
        fi
    elif [ "$MODE" = "ping" ]; then
        if [ $VERBOSE -eq 1 ]; then
            echo -e "${BLUE}Ping测试 $host (次数: ${PING_COUNT})${NC}"
        fi

        # 执行ping测试
        if ping -c "$PING_COUNT" -W "$TIMEOUT" "$host" >/dev/null 2>&1; then
            status="成功"
        else
            status="失败"
        fi
    fi

    # 计算测试用时
    local end_time=$(date +%s.%N)
    duration=$(awk "BEGIN {print $end_time - $start_time}")

    # 保存结果
    local display_host_port
    if [ "$MODE" = "ping" ]; then
        display_host_port="$host (ping)"
    else
        display_host_port="$host:$port"
    fi

    # 获取当前测试时间
    local test_timestamp=$(date '+%Y-%m-%d %H:%M:%S')

    echo "$display_host_port,$status,$MODE,$duration,$test_timestamp" >>"$RESULTS_FILE"

    # 如果不是并行模式，直接输出结果
    if [ $PARALLEL -eq 0 ]; then
        display_result "$display_host_port" "$status" "$duration"
    fi
}

# 函数：显示单个测试结果
display_result() {
    local host_port=$1
    local status=$2
    local duration=$3

    # 格式化时间（保留3位小数）
    duration=$(printf "%.3f" $duration)

    if [ "$status" = "成功" ]; then
        echo -e "${GREEN}[✓] $host_port - 连接${status} (${duration}s)${NC}"
    else
        echo -e "${RED}[✗] $host_port - 连接${status} (${duration}s)${NC}"
    fi
}

# 函数：处理测试任务
process_task() {
    local task=$1
    test_connection "$task"
}

# 函数：执行并行测试
run_parallel_tests() {
    echo -e "${BLUE}开始并行测试 (最大并行任务数: $MAX_PARALLEL)...${NC}"

    # 检查是否有GNU parallel
    if command -v parallel &>/dev/null; then
        # 使用GNU parallel进行并行执行
        cat "$TASKS_FILE" | parallel -j "$MAX_PARALLEL" "$0 --internal-test {}"
    else
        # 使用后台作业进行基本的并行处理
        local running=0
        local total_tasks=$(wc -l <"$TASKS_FILE")
        local processed=0

        while read -r task || [ -n "$task" ]; do
            # 限制并行任务数
            while [ $running -ge $MAX_PARALLEL ]; do
                running=$(jobs -r | wc -l)
                sleep 0.1
            done

            # 启动后台任务
            (process_task "$task") &
            running=$(jobs -r | wc -l)

            # 更新进度
            processed=$((processed + 1))
            if [ $VERBOSE -eq 1 ]; then
                echo -e "${BLUE}进度: $processed/$total_tasks 任务已启动 (当前运行: $running)${NC}"
            fi
        done <"$TASKS_FILE"

        # 等待所有后台任务完成
        wait
    fi
}

# 函数：处理内部测试请求（由parallel调用）
handle_internal_test() {
    local input=$1
    local parsed_result
    local host
    local port
    local start_time=$(date +%s.%N)
    local status

    # 解析输入格式
    parsed_result=$(parse_host_port "$input")
    if [ $? -ne 0 ] || [ -z "$parsed_result" ]; then
        return 1
    fi

    # 根据模式提取主机和端口
    if [ "$MODE" = "ping" ]; then
        host="$parsed_result"
        port="PING"
    else
        host=${parsed_result%:*}
        port=${parsed_result#*:}
    fi

    # 根据协议进行不同测试
    if [ "$MODE" = "tcp" ]; then
        if nc -z -w "$TIMEOUT" "$host" "$port" 2>/dev/null; then
            status="成功"
        else
            status="失败"
        fi
    elif [ "$MODE" = "udp" ]; then
        if nc -z -u -w "$TIMEOUT" "$host" "$port" 2>/dev/null; then
            status="成功"
        else
            status="失败"
        fi
    elif [ "$MODE" = "ping" ]; then
        if ping -c "$PING_COUNT" -W "$TIMEOUT" "$host" >/dev/null 2>&1; then
            status="成功"
        else
            status="失败"
        fi
    fi

    # 计算测试用时
    local end_time=$(date +%s.%N)
    local duration=$(awk "BEGIN {print $end_time - $start_time}")

    # 将结果添加到结果文件中（加锁以防止文件冲突）
    {
        local display_host_port
        if [ "$MODE" = "ping" ]; then
            display_host_port="$host (ping)"
        else
            display_host_port="$host:$port"
        fi
        # 获取当前测试时间
        local test_timestamp=$(date '+%Y-%m-%d %H:%M:%S')
        echo "$display_host_port,$status,$MODE,$duration,$test_timestamp" >>"$RESULTS_FILE"
    }
}

# 函数：统计和显示结果
show_summary() {
    echo ""
    echo -e "${BLUE}=== 测试摘要 ===${NC}"
    echo -e "协议: ${MODE}"
    echo -e "总测试数: ${TOTAL}"
    echo -e "成功: ${GREEN}${SUCCESS}${NC}"
    echo -e "失败: ${RED}${FAILED}${NC}"
    echo -e "总用时: $(printf "%.2f" $TOTAL_TIME) 秒"
    echo ""
}

# 函数：Excel导出（Excel/CSV格式）
export_to_excel() {
    local excel_file=$1
    local csv_file=$2

    echo -e "${BLUE}📊 正在导出Excel文件: $excel_file${NC}"

    # 检测可用的导出方式
    local export_method=""

    # 优先级1：检测完整Excel支持
    if command -v python3 &>/dev/null && python3 -c "import openpyxl" &>/dev/null 2>&1; then
        export_method="excel"
        echo -e "${GREEN}✅ 使用完整Excel格式 (openpyxl)${NC}"
    # 优先级2：检测Python支持（用于增强CSV）
    elif command -v python3 &>/dev/null; then
        export_method="enhanced_csv"
        echo -e "${YELLOW}⚠️  使用增强CSV格式 (缺少openpyxl库)${NC}"
    else
        echo -e "${RED}❌ Excel导出失败: 缺少Python3环境${NC}"
        return 1
    fi

    # 根据检测结果选择导出方式
    case $export_method in
        "excel")
            export_to_excel_full "$excel_file" "$csv_file"
            ;;
        "enhanced_csv")
            export_to_enhanced_csv "$excel_file" "$csv_file"
            ;;
    esac
}

# 函数：导出完整Excel文件
export_to_excel_full() {
    local excel_file=$1
    local csv_file=$2

    # 创建Python脚本来生成Excel文件
    cat > "${TMP_DIR}/excel_export.py" << 'EOF'
#!/usr/bin/env python3
import sys
import csv
import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment
from openpyxl.utils import get_column_letter
from datetime import datetime

def create_excel_from_csv(csv_file, excel_file):
    # 创建新的工作簿
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "端口连通性测试结果"

    # 设置表头
    headers = ["IP", "端口", "协议", "状态", "测试时间"]
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col, value=header)
        # 设置表头样式
        cell.font = Font(bold=True, color="FFFFFF")
        cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        cell.alignment = Alignment(horizontal="center", vertical="center")

    # 读取CSV文件并写入Excel
    row_num = 2
    try:
        with open(csv_file, 'r', encoding='utf-8') as f:
            # 跳过注释行和空行
            lines = [line.strip() for line in f if line.strip() and not line.startswith('#')]

            # 找到CSV数据开始的位置（跳过标题行）
            data_start = 0
            for i, line in enumerate(lines):
                if '主机:端口,状态,协议,耗时' in line:
                    data_start = i + 1
                    break

            # 处理数据行
            for line in lines[data_start:]:
                if not line or line.startswith('#'):
                    continue

                parts = line.split(',')
                if len(parts) >= 5:
                    host_port = parts[0].strip()
                    status = parts[1].strip()
                    protocol = parts[2].strip()
                    duration = parts[3].strip() if len(parts) > 3 else "0"
                    test_time = parts[4].strip() if len(parts) > 4 else datetime.now().strftime("%Y-%m-%d %H:%M:%S")

                    # 解析IP和端口
                    if '(ping)' in host_port:
                        ip = host_port.replace(' (ping)', '')
                        port = 'PING'
                    elif ':' in host_port:
                        ip, port = host_port.rsplit(':', 1)
                    else:
                        ip = host_port
                        port = 'N/A'

                    # 写入数据
                    ws.cell(row=row_num, column=1, value=ip)
                    ws.cell(row=row_num, column=2, value=port)
                    ws.cell(row=row_num, column=3, value=protocol.upper())
                    ws.cell(row=row_num, column=5, value=test_time)

                    # 设置状态单元格样式
                    status_cell = ws.cell(row=row_num, column=4, value=status)
                    if status == "成功":
                        status_cell.fill = PatternFill(start_color="C6EFCE", end_color="C6EFCE", fill_type="solid")
                        status_cell.font = Font(color="006100")
                    else:
                        status_cell.fill = PatternFill(start_color="FFC7CE", end_color="FFC7CE", fill_type="solid")
                        status_cell.font = Font(color="9C0006")

                    row_num += 1

    except Exception as e:
        print(f"读取CSV文件时出错: {e}")
        return False

    # 自动调整列宽
    for column in ws.columns:
        max_length = 0
        column_letter = get_column_letter(column[0].column)
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)
        ws.column_dimensions[column_letter].width = adjusted_width

    # 添加筛选功能
    if row_num > 2:  # 如果有数据行
        ws.auto_filter.ref = f"A1:E{row_num-1}"

    # 冻结首行
    ws.freeze_panes = "A2"

    # 添加统计信息到新的工作表
    stats_ws = wb.create_sheet("统计信息")
    stats_ws.cell(row=1, column=1, value="测试统计").font = Font(bold=True, size=14)
    stats_ws.cell(row=2, column=1, value="生成时间:")
    stats_ws.cell(row=2, column=2, value=datetime.now().strftime("%Y-%m-%d %H:%M:%S"))

    # 计算统计数据
    total_tests = row_num - 2
    success_count = 0
    failed_count = 0

    for row in range(2, row_num):
        status = ws.cell(row=row, column=4).value
        if status == "成功":
            success_count += 1
        else:
            failed_count += 1

    stats_ws.cell(row=3, column=1, value="总测试数:")
    stats_ws.cell(row=3, column=2, value=total_tests)
    stats_ws.cell(row=4, column=1, value="成功数:")
    stats_ws.cell(row=4, column=2, value=success_count)
    stats_ws.cell(row=5, column=1, value="失败数:")
    stats_ws.cell(row=5, column=2, value=failed_count)
    stats_ws.cell(row=6, column=1, value="成功率:")
    stats_ws.cell(row=6, column=2, value=f"{(success_count/total_tests*100):.1f}%" if total_tests > 0 else "0%")

    # 保存文件
    try:
        wb.save(excel_file)
        return True
    except Exception as e:
        print(f"保存Excel文件时出错: {e}")
        return False

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("用法: python3 excel_export.py <csv_file> <excel_file>")
        sys.exit(1)

    csv_file = sys.argv[1]
    excel_file = sys.argv[2]

    if create_excel_from_csv(csv_file, excel_file):
        print(f"✅ Excel文件已成功创建: {excel_file}")
    else:
        print("❌ Excel文件创建失败")
        sys.exit(1)
EOF

    # 执行Python脚本
    if python3 "${TMP_DIR}/excel_export.py" "$csv_file" "$excel_file"; then
        echo -e "${GREEN}✅ Excel文件导出成功: $excel_file${NC}"
        echo -e "${BLUE}📋 Excel文件包含以下功能:${NC}"
        echo -e "  • 可筛选的表头"
        echo -e "  • 状态颜色标识（成功=绿色，失败=红色）"
        echo -e "  • 自动调整列宽"
        echo -e "  • 冻结首行"
        echo -e "  • 统计信息工作表"
        return 0
    else
        echo -e "${RED}❌ Excel文件导出失败${NC}"
        return 1
    fi
}

# 函数：导出增强CSV文件
export_to_enhanced_csv() {
    local excel_file=$1
    local csv_file=$2
    local output_file="${excel_file%.xlsx}.csv"

    echo -e "${BLUE}📄 正在生成增强CSV文件: $output_file${NC}"

    # 创建Python脚本来生成增强CSV
    cat > "${TMP_DIR}/enhanced_csv_export.py" << 'EOF'
#!/usr/bin/env python3
import sys
import csv
from datetime import datetime

def create_enhanced_csv(csv_file, output_file):
    # 读取原始CSV数据
    data_rows = []
    stats = {"total": 0, "success": 0, "failed": 0}

    try:
        with open(csv_file, 'r', encoding='utf-8') as f:
            lines = [line.strip() for line in f if line.strip() and not line.startswith('#')]

            # 找到CSV数据开始的位置
            data_start = 0
            for i, line in enumerate(lines):
                if '主机:端口,状态,协议,耗时' in line:
                    data_start = i + 1
                    break

            # 处理数据行
            for line in lines[data_start:]:
                if not line or line.startswith('#'):
                    continue

                parts = line.split(',')
                if len(parts) >= 5:
                    host_port = parts[0].strip()
                    status = parts[1].strip()
                    protocol = parts[2].strip()
                    duration = parts[3].strip() if len(parts) > 3 else "0"
                    test_time = parts[4].strip() if len(parts) > 4 else datetime.now().strftime("%Y-%m-%d %H:%M:%S")

                    # 解析IP和端口
                    if '(ping)' in host_port:
                        ip = host_port.replace(' (ping)', '')
                        port = 'PING'
                    elif ':' in host_port:
                        ip, port = host_port.rsplit(':', 1)
                    else:
                        ip = host_port
                        port = 'N/A'

                    # 状态emoji标识
                    status_display = "✅ 成功" if status == "成功" else "❌ 失败"

                    data_rows.append([ip, port, protocol.upper(), status_display, test_time])
                    stats["total"] += 1
                    if status == "成功":
                        stats["success"] += 1
                    else:
                        stats["failed"] += 1

    except Exception as e:
        print(f"读取CSV文件时出错: {e}")
        return False

    # 写入增强CSV文件
    try:
        with open(output_file, 'w', encoding='utf-8-sig', newline='') as f:
            # 写入BOM头确保Excel正确显示中文
            writer = csv.writer(f)


            # 写入表头
            writer.writerow(['IP', '端口', '协议', '状态', '测试时间'])

            # 写入数据
            for row in data_rows:
                writer.writerow(row)

        return True

    except Exception as e:
        print(f"写入CSV文件时出错: {e}")
        return False

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("用法: python3 enhanced_csv_export.py <csv_file> <output_file>")
        sys.exit(1)

    csv_file = sys.argv[1]
    output_file = sys.argv[2]

    if create_enhanced_csv(csv_file, output_file):
        print(f"✅ 增强CSV文件已成功创建: {output_file}")
    else:
        print("❌ 增强CSV文件创建失败")
        sys.exit(1)
EOF

    # 执行Python脚本
    if python3 "${TMP_DIR}/enhanced_csv_export.py" "$csv_file" "$output_file"; then
        echo -e "${GREEN}✅ 增强CSV文件导出成功: $output_file${NC}"
        echo -e "${BLUE}📋 增强CSV文件特性:${NC}"
        echo -e "  • UTF-8编码，Excel可正确显示中文"
        echo -e "  • 状态emoji标识（✅❌）"
        echo -e "  • 兼容Excel筛选功能"
        echo -e "  • 包含统计信息"
        echo -e "${YELLOW}💡 使用提示: 直接用Excel打开此文件即可${NC}"
        return 0
    else
        echo -e "${RED}❌ 增强CSV文件导出失败${NC}"
        return 1
    fi
}

# 函数：导出HTML表格文件
export_to_html() {
    local html_file=$1
    local csv_file=$2
    local output_file="$html_file"

    echo -e "${BLUE}🌐 正在生成HTML表格文件: $output_file${NC}"

    # 读取CSV数据并生成HTML
    local html_content=""
    local data_rows=""
    local total=0
    local success=0
    local failed=0

    # 处理CSV数据
    while IFS= read -r line || [ -n "$line" ]; do
        # 跳过注释和空行
        if [[ $line =~ ^#.*$ ]] || [[ -z "$line" ]] || [[ $line =~ ^主机:端口,状态,协议,耗时.*$ ]]; then
            continue
        fi

        # 解析数据行
        IFS=',' read -r host_port status protocol duration test_timestamp <<< "$line"

        # 解析IP和端口
        local ip port
        if [[ $host_port =~ \(ping\) ]]; then
            ip="${host_port% (ping)}"
            port="PING"
        elif [[ $host_port =~ : ]]; then
            ip="${host_port%:*}"
            port="${host_port##*:}"
        else
            ip="$host_port"
            port="N/A"
        fi

        # 状态样式
        local status_class status_display
        if [[ "$status" == "成功" ]]; then
            status_class="success"
            status_display="✅ 成功"
            ((success++))
        else
            status_class="failed"
            status_display="❌ 失败"
            ((failed++))
        fi

        # 使用从CSV读取的测试时间，如果没有则使用当前时间
        local test_time="${test_timestamp:-$(date '+%Y-%m-%d %H:%M:%S')}"

        ((total++))

        # 添加表格行
        data_rows+="        <tr>
            <td>$ip</td>
            <td>$port</td>
            <td>${protocol^^}</td>
            <td class=\"$status_class\">$status_display</td>
            <td>$test_time</td>
        </tr>
"
    done < "$csv_file"

    # 计算成功率
    local success_rate=0
    if [ $total -gt 0 ]; then
        success_rate=$(awk "BEGIN {printf \"%.1f\", $success/$total*100}")
    fi

    # 生成HTML文件
    cat > "$output_file" << EOF
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>端口连通性测试结果</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #366092;
            padding-bottom: 10px;
        }
        .stats {
            display: flex;
            justify-content: space-around;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .stat-item {
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #366092;
        }
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
        .controls {
            margin-bottom: 20px;
            padding: 10px;
            background: #e9ecef;
            border-radius: 5px;
        }
        .filter-input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-right: 10px;
            width: 200px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #366092;
            color: white;
            font-weight: bold;
            cursor: pointer;
            user-select: none;
        }
        th:hover {
            background-color: #2a4d73;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .success {
            color: #006100;
            background-color: #C6EFCE;
            padding: 4px 8px;
            border-radius: 3px;
        }
        .failed {
            color: #9C0006;
            background-color: #FFC7CE;
            padding: 4px 8px;
            border-radius: 3px;
        }
        .footer {
            margin-top: 20px;
            text-align: center;
            color: #666;
            font-size: 12px;
        }
        @media print {
            body { background: white; }
            .container { box-shadow: none; }
            .controls { display: none; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 端口连通性测试结果</h1>

        <div class="stats">
            <div class="stat-item">
                <div class="stat-value">$total</div>
                <div class="stat-label">总测试数</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" style="color: #28a745;">$success</div>
                <div class="stat-label">成功数</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" style="color: #dc3545;">$failed</div>
                <div class="stat-label">失败数</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" style="color: #17a2b8;">$success_rate%</div>
                <div class="stat-label">成功率</div>
            </div>
        </div>

        <div class="controls">
            <input type="text" id="filterInput" class="filter-input" placeholder="输入关键词筛选..." onkeyup="filterTable()">
            <button onclick="clearFilter()">清除筛选</button>
            <button onclick="window.print()">打印</button>
        </div>

        <table id="resultsTable">
            <thead>
                <tr>
                    <th onclick="sortTable(0)">IP 📶</th>
                    <th onclick="sortTable(1)">端口 🔌</th>
                    <th onclick="sortTable(2)">协议 📡</th>
                    <th onclick="sortTable(3)">状态 📊</th>
                    <th onclick="sortTable(4)">测试时间 🕐</th>
                </tr>
            </thead>
            <tbody>
$data_rows
            </tbody>
        </table>

        <div class="footer">
            <p>生成时间: $(date '+%Y-%m-%d %H:%M:%S') | 端口连通性测试脚本 v1.2</p>
            <p>💡 提示: 点击表头可排序，使用筛选框可快速查找</p>
        </div>
    </div>

    <script>
        function filterTable() {
            const input = document.getElementById('filterInput');
            const filter = input.value.toLowerCase();
            const table = document.getElementById('resultsTable');
            const rows = table.getElementsByTagName('tr');

            for (let i = 1; i < rows.length; i++) {
                const row = rows[i];
                const cells = row.getElementsByTagName('td');
                let found = false;

                for (let j = 0; j < cells.length; j++) {
                    if (cells[j].textContent.toLowerCase().includes(filter)) {
                        found = true;
                        break;
                    }
                }

                row.style.display = found ? '' : 'none';
            }
        }

        function clearFilter() {
            document.getElementById('filterInput').value = '';
            filterTable();
        }

        function sortTable(columnIndex) {
            const table = document.getElementById('resultsTable');
            const tbody = table.getElementsByTagName('tbody')[0];
            const rows = Array.from(tbody.getElementsByTagName('tr'));

            rows.sort((a, b) => {
                const aText = a.getElementsByTagName('td')[columnIndex].textContent.trim();
                const bText = b.getElementsByTagName('td')[columnIndex].textContent.trim();
                return aText.localeCompare(bText);
            });

            rows.forEach(row => tbody.appendChild(row));
        }
    </script>
</body>
</html>
EOF

    if [ -f "$output_file" ]; then
        echo -e "${GREEN}✅ HTML表格文件导出成功: $output_file${NC}"
        echo -e "${BLUE}📋 HTML文件特性:${NC}"
        echo -e "  • 响应式表格设计"
        echo -e "  • 状态颜色标识"
        echo -e "  • 实时筛选和排序功能"
        echo -e "  • 统计信息展示"
        echo -e "  • 打印友好样式"
        echo -e "${YELLOW}💡 使用提示: 可用浏览器或Excel打开此文件${NC}"
        return 0
    else
        echo -e "${RED}❌ HTML文件导出失败${NC}"
        return 1
    fi
}

# 函数：清理临时文件
cleanup() {
    rm -rf "$TMP_DIR"
}

# 主程序开始
# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
    -h | --help)
        show_help
        exit 0
        ;;
    -f | --file)
        if [ -z "$2" ]; then
            echo -e "${RED}错误: -f/--file 参数需要指定文件名${NC}"
            exit 1
        fi
        INPUT_FILE="$2"
        shift 2
        ;;
    -t | --timeout)
        if [ -z "$2" ]; then
            echo -e "${RED}错误: -t/--timeout 参数需要指定超时时间${NC}"
            exit 1
        fi
        TIMEOUT="$2"
        shift 2
        ;;
    -p | --protocol)
        if [ -z "$2" ]; then
            echo -e "${RED}错误: -p/--protocol 参数需要指定协议类型${NC}"
            exit 1
        fi
        MODE="$2"
        if [[ "$MODE" != "tcp" && "$MODE" != "udp" && "$MODE" != "ping" ]]; then
            echo -e "${RED}错误: 协议必须是 'tcp'、'udp' 或 'ping'${NC}"
            exit 1
        fi
        shift 2
        ;;
    -c | --count)
        if [ -z "$2" ]; then
            echo -e "${RED}错误: -c/--count 参数需要指定ping次数${NC}"
            exit 1
        fi
        PING_COUNT="$2"
        shift 2
        ;;
    -v | --verbose)
        VERBOSE=1
        shift
        ;;
    -o | --output)
        if [ -z "$2" ]; then
            echo -e "${RED}错误: -o/--output 参数需要指定输出文件名${NC}"
            exit 1
        fi
        OUTPUT_FILE="$2"
        shift 2
        ;;
    -e | --excel)
        if [ -z "$2" ]; then
            echo -e "${RED}错误: -e/--excel 参数需要指定文件名${NC}"
            exit 1
        fi
        EXCEL_FILE="$2"
        shift 2
        ;;
    -H | --html)
        if [ -z "$2" ]; then
            echo -e "${RED}错误: -H/--html 参数需要指定文件名${NC}"
            exit 1
        fi
        HTML_FILE="$2"
        shift 2
        ;;
    -P | --parallel)
        PARALLEL=1
        shift
        ;;
    -j | --jobs)
        if [ -z "$2" ]; then
            echo -e "${RED}错误: -j/--jobs 参数需要指定并行任务数${NC}"
            exit 1
        fi
        MAX_PARALLEL="$2"
        shift 2
        ;;
    --internal-test)
        # 内部使用的标志，用于并行处理
        handle_internal_test "$2"
        exit 0
        ;;
    *)
        # 检查是否是有效的主机端口格式
        local parsed_result
        parsed_result=$(parse_host_port "$1")

        if [ $? -eq 0 ] && [ -n "$parsed_result" ]; then
            # 有效的主机端口格式，添加到任务列表
            echo "$1" >>"$TASKS_FILE"
        elif [ "$MODE" = "ping" ]; then
            # ping模式允许单独的主机名
            echo "$1" >>"$TASKS_FILE"
        else
            # 检查是否可能是空格分隔的格式（需要下一个参数）
            if [[ $1 =~ ^[^[:space:]]+$ ]] && [[ $2 =~ ^[0-9]+$ ]] && [ $# -gt 1 ]; then
                # 这是空格分隔的格式: host port
                echo "$1 $2" >>"$TASKS_FILE"
                shift 2
                continue
            else
                echo -e "${RED}错误: 无法识别的参数 '$1'${NC}"
                echo -e "${YELLOW}支持的格式:${NC}"
                echo -e "  • 主机:端口 (如: ***********:22)"
                echo -e "  • 主机 端口 (如: *********** 22)"
                echo -e "  • 主机名 (ping模式)"
                show_help
                exit 1
            fi
        fi
        shift
        ;;
    esac
done

# 检查依赖工具
check_dependencies

# 初始化结果文件
echo '主机:端口,状态,协议,耗时(秒),测试时间' >"$RESULTS_FILE"

# 如果提供了输入文件，从文件中读取主机和端口
if [ -n "$INPUT_FILE" ]; then
    if [ ! -f "$INPUT_FILE" ]; then
        echo -e "${RED}错误: 找不到输入文件 '$INPUT_FILE'${NC}"
        exit 1
    fi

    # 将输入文件内容添加到任务列表
    cat "$INPUT_FILE" >>"$TASKS_FILE"
fi

# 检查任务文件是否为空
if [ ! -s "$TASKS_FILE" ]; then
    echo -e "${YELLOW}警告: 没有指定要测试的主机和端口${NC}"
    show_help
    exit 1
fi

# 记录开始时间
START_TIME=$(date +%s.%N)

# 执行测试
if [ $PARALLEL -eq 1 ]; then
    run_parallel_tests
else
    # 串行执行
    while read -r line || [ -n "$line" ]; do
        test_connection "$line"
    done <"$TASKS_FILE"
fi

# 记录结束时间
END_TIME=$(date +%s.%N)
TOTAL_TIME=$(awk "BEGIN {print $END_TIME - $START_TIME}")

# 如果是并行模式，显示所有结果
if [ $PARALLEL -eq 1 ]; then
    echo -e "
${BLUE}测试结果:${NC}"
    # 跳过标题行
    tail -n +2 "$RESULTS_FILE" | while IFS=, read -r host_port status mode duration; do
        display_result "$host_port" "$status" "$duration"
    done
fi

# 统计结果
TOTAL=$(tail -n +2 "$RESULTS_FILE" | wc -l)
SUCCESS=$(grep ",成功," "$RESULTS_FILE" | wc -l)
FAILED=$((TOTAL - SUCCESS))

# 显示摘要
show_summary

# 如果指定了输出文件，将结果保存
if [ -n "$OUTPUT_FILE" ]; then
    # 创建详细的CSV输出
    {
        echo "# 端口连通性测试结果"
        echo "# 生成时间: $(date)"
        echo "# 总测试数: $TOTAL"
        echo "# 成功: $SUCCESS, 失败: $FAILED"
        echo "# 协议: $MODE"
        echo "# 总用时: $(printf "%.2f" $TOTAL_TIME) 秒"
        echo ""
        cat "$RESULTS_FILE"
    } >"$OUTPUT_FILE"

    echo -e "${GREEN}📄 CSV结果已保存到: $OUTPUT_FILE${NC}"
fi

# 如果指定了Excel文件，导出到Excel
if [ -n "$EXCEL_FILE" ]; then
    # 创建临时CSV文件用于Excel导出
    TEMP_CSV="${TMP_DIR}/temp_results.csv"
    {
        echo "# 端口连通性测试结果"
        echo "# 生成时间: $(date)"
        echo "# 总测试数: $TOTAL"
        echo "# 成功: $SUCCESS, 失败: $FAILED"
        echo "# 协议: $MODE"
        echo "# 总用时: $(printf "%.2f" $TOTAL_TIME) 秒"
        echo ""
        cat "$RESULTS_FILE"
    } >"$TEMP_CSV"

    # 导出到Excel
    export_to_excel "$EXCEL_FILE" "$TEMP_CSV"
fi

# 如果指定了HTML文件，导出到HTML
if [ -n "$HTML_FILE" ]; then
    # 创建临时CSV文件用于HTML导出
    TEMP_CSV="${TMP_DIR}/temp_results.csv"
    {
        echo "# 端口连通性测试结果"
        echo "# 生成时间: $(date)"
        echo "# 总测试数: $TOTAL"
        echo "# 成功: $SUCCESS, 失败: $FAILED"
        echo "# 协议: $MODE"
        echo "# 总用时: $(printf "%.2f" $TOTAL_TIME) 秒"
        echo ""
        cat "$RESULTS_FILE"
    } >"$TEMP_CSV"

    # 导出到HTML
    export_to_html "$HTML_FILE" "$TEMP_CSV"
fi

# 清理
trap cleanup EXIT
exit 0