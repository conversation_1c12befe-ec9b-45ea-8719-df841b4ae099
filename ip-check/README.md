# 端口连通性批量测试脚本

## 📋 功能概述

这是一个功能强大的端口连通性批量测试脚本，支持TCP、UDP和Ping测试，现在新增了Excel导出功能！

## ✨ 新增功能

### 🎯 智能导出功能（条件降级）
脚本会自动检测环境并选择最佳的导出格式：

#### 📊 优先级1：完整Excel格式
**条件**: Python3 + openpyxl库
- **表头设置**: IP、端口、协议、状态
- **可筛选表头**: 支持Excel自动筛选功能
- **状态颜色标识**: 🟢 成功=绿色，🔴 失败=红色
- **自动调整列宽**: 根据内容自动调整
- **冻结首行**: 方便查看大量数据
- **统计信息工作表**: 包含测试摘要和成功率

#### 📄 优先级2：增强CSV格式
**条件**: Python3（无openpyxl库）
- **UTF-8编码**: Excel可正确显示中文
- **状态emoji标识**: ✅ 成功，❌ 失败
- **兼容Excel筛选**: 可直接用Excel打开并筛选
- **包含统计信息**: 文件头包含测试摘要

#### 🌐 优先级3：HTML表格格式
**条件**: 无Python3或作为备选
- **响应式设计**: 适配各种屏幕尺寸
- **状态颜色标识**: CSS样式实现颜色区分
- **实时筛选排序**: JavaScript实现交互功能
- **统计信息展示**: 页面顶部显示测试摘要
- **打印友好**: 支持打印输出
- **双重兼容**: 浏览器和Excel都能打开

## 🚀 使用方法

### 基本用法
```bash
# 测试单个主机端口（冒号分隔）
./ip-check.sh google.com:80 github.com:443

# 测试单个主机端口（空格分隔）
./ip-check.sh *********** 22 localhost 80

# 混合格式
./ip-check.sh google.com:80 *********** 22

# 从文件读取主机列表并导出到Excel
./ip-check.sh --file hosts.txt --excel results.xlsx

# 并行测试并导出Excel
./ip-check.sh --parallel --file hosts.txt --excel results.xlsx
```

### 命令行参数
```
选项:
  -h, --help            显示此帮助信息
  -f, --file FILE       从指定文件读取主机和端口列表（支持两种格式）
  -t, --timeout SEC     设置连接超时时间（默认：3秒）
  -p, --protocol PROTO  设置协议类型，tcp、udp或ping（默认：tcp）
  -c, --count NUM       设置ping测试的次数（默认：3次）
  -v, --verbose         输出详细信息
  -o, --output FILE     将结果输出到CSV文件
  -e, --excel FILE      智能导出文件（Excel/CSV/HTML自适应）📊
  -P, --parallel        启用并行测试
  -j, --jobs NUM        最大并行任务数（默认：10）
```

## 📁 文件格式示例

### hosts.txt
```
# 主机列表文件 - 支持两种格式
# 冒号分隔格式
google.com:80
github.com:443
baidu.com:80
# 空格分隔格式
*********** 22
localhost 22
127.0.0.1 80
# 混合使用也可以
example.com:443
test.local 8080
```

## 🔧 依赖要求

### 基本依赖
- `nc` (netcat) - 用于端口测试
- `ping` - 用于ping测试

### 智能导出依赖（可选）
- `python3` - Python 3.x（增强CSV和完整Excel功能）
- `openpyxl` - Python Excel库（完整Excel功能，可选安装）

**注意**: 所有依赖都是可选的，脚本会根据环境自动降级到合适的格式

### 安装依赖
```bash
# Ubuntu/Debian
sudo apt-get install netcat python3 python3-pip

# CentOS/RHEL
sudo yum install nc python3 python3-pip

# macOS
brew install netcat python3
```

## 📊 Excel输出示例

生成的Excel文件包含：

### 主工作表 - "端口连通性测试结果"
| IP | 端口 | 协议 | 状态 |
|---|---|---|---|
| google.com | 80 | TCP | 成功 ✅ |
| github.com | 443 | TCP | 成功 ✅ |
| *********** | 22 | TCP | 失败 ❌ |

### 统计信息工作表
- 生成时间
- 总测试数
- 成功数
- 失败数
- 成功率

## 🎯 使用示例

### 1. 基本TCP端口测试
```bash
# 冒号分隔格式
./ip-check.sh google.com:80 github.com:443

# 空格分隔格式
./ip-check.sh *********** 22 localhost 80

# 混合格式
./ip-check.sh google.com:80 *********** 22
```

### 2. 从文件批量测试并导出Excel
```bash
./ip-check.sh --file hosts.txt --excel network_test.xlsx
```

### 3. Ping测试
```bash
./ip-check.sh --protocol ping google.com baidu.com
```

### 4. 并行测试提高效率
```bash
./ip-check.sh --parallel --jobs 20 --file large_hosts.txt --excel results.xlsx
```

### 5. 详细输出模式
```bash
./ip-check.sh --verbose --file hosts.txt --excel detailed_results.xlsx
```

## 🔍 输出说明

### 控制台输出
- 🟢 `[✓]` - 连接成功
- 🔴 `[✗]` - 连接失败
- 📊 测试摘要统计

### Excel文件特性
- 📋 可筛选表头
- 🎨 状态颜色编码
- 📏 自动列宽调整
- 🔒 冻结首行
- 📈 统计信息工作表

## 🚨 注意事项

1. **UDP测试限制**: UDP端口检测可能不够准确，因为UDP是无连接协议
2. **权限要求**: 某些端口测试可能需要管理员权限
3. **防火墙影响**: 防火墙设置可能影响测试结果
4. **网络延迟**: 超时设置应根据网络环境调整

## 🐛 故障排除

### 常见问题

1. **找不到nc命令**
   ```bash
   # Ubuntu/Debian
   sudo apt-get install netcat
   
   # CentOS/RHEL
   sudo yum install nc
   ```

2. **Python依赖问题**
   ```bash
   # 手动安装openpyxl
   pip install openpyxl
   # 或
   python -m pip install openpyxl
   ```

3. **权限问题**
   ```bash
   chmod +x ip-check.sh
   ```

## 📝 更新日志

### v1.2 (最新)
- ✨ 新增Excel导出功能
- 📊 支持可筛选表头
- 🎨 状态颜色标识
- 📈 统计信息工作表
- 🔧 自动安装Python依赖

### v1.1
- 🚀 并行测试支持
- 🏓 Ping测试功能
- 📄 CSV输出支持

## 👨‍💻 作者

- 作者：jqa
- 版本：1.2
- 更新：2025-01-21
